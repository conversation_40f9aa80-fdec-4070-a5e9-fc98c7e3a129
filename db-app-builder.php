<?php
/**
 * Plugin Name: Database App Builder
 * Plugin URI: https://yourdomain.com/database-app-builder
 * Description: Build custom database-driven applications inside WordPress without coding.
 * Version: 1.0.2
 * Author: Your Name
 * Author URI: https://yourdomain.com
 * Text Domain: db-app-builder
 * Domain Path: /languages
 * Requires at least: 5.0
 * Requires PHP: 7.0
 */

if (!defined('ABSPATH')) {
    exit;
}

// Prevent any output during plugin loading to avoid "headers already sent" errors
if (!defined('DOING_AJAX') && !defined('WP_CLI')) {
    ob_start();
}

// Enhanced error handling for PHP 8.1+ compatibility
$original_error_reporting = error_reporting();
if (version_compare(PHP_VERSION, '8.1', '>=')) {
    error_reporting(E_ALL & ~E_DEPRECATED);
}

// Enhanced custom error handler to catch and suppress specific deprecated warnings
function dab_error_handler($errno, $errstr, $errfile, $errline) {
    // Suppress specific deprecated warnings that come from WordPress core
    if ($errno === E_DEPRECATED) {
        $deprecated_patterns = array(
            'strpos(): Passing null to parameter',
            'str_replace(): Passing null to parameter',
            'strlen(): Passing null to parameter',
            'substr(): Passing null to parameter',
            'trim(): Passing null to parameter',
            'explode(): Passing null to parameter'
        );

        foreach ($deprecated_patterns as $pattern) {
            if (strpos($errstr, $pattern) !== false) {
                return true; // Suppress the error
            }
        }
    }
    return false; // Let other errors through
}

// Global function to safely handle string operations
if (!function_exists('dab_safe_string')) {
    function dab_safe_string($value, $default = '') {
        return ($value !== null) ? (string)$value : $default;
    }
}

// Global function to safely handle strpos operations
if (!function_exists('dab_safe_strpos')) {
    function dab_safe_strpos($haystack, $needle, $offset = 0) {
        $haystack = dab_safe_string($haystack);
        $needle = dab_safe_string($needle);
        return strpos($haystack, $needle, $offset);
    }
}

// Global function to safely handle str_replace operations
if (!function_exists('dab_safe_str_replace')) {
    function dab_safe_str_replace($search, $replace, $subject) {
        $search = is_array($search) ? $search : dab_safe_string($search);
        $replace = is_array($replace) ? $replace : dab_safe_string($replace);
        $subject = is_array($subject) ? $subject : dab_safe_string($subject);
        return str_replace($search, $replace, $subject);
    }
}

// Set the custom error handler
set_error_handler('dab_error_handler', E_DEPRECATED);

// Add WordPress filters to prevent null parameter issues
add_filter('admin_enqueue_scripts', function($hook) {
    return dab_safe_string($hook);
}, 1);

// Global WordPress hook sanitization
add_action('init', function() {
    // Sanitize common WordPress hooks that might receive null values
    $hooks_to_sanitize = array(
        'admin_enqueue_scripts',
        'wp_enqueue_scripts',
        'admin_head',
        'wp_head',
        'admin_footer',
        'wp_footer'
    );

    foreach ($hooks_to_sanitize as $hook_name) {
        add_filter($hook_name, function($value) {
            return dab_safe_string($value);
        }, 1);
    }
}, 1);

// Enable error reporting for debugging
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
}

// Define plugin constants
define('DAB_VERSION', '1.0.4');
define('DAB_PLUGIN_FILE', __FILE__);
define('DAB_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAB_PLUGIN_URL', plugin_dir_url(__FILE__));
define('DAB_ITEM_ID', 12345); // Replace with your actual item ID from your store

// Load Core Files with error handling
try {
    require_once plugin_dir_path(__FILE__) . 'admin/class-admin-menu.php';
    require_once plugin_dir_path(__FILE__) . 'admin/class-field-types.php'; // Field types manager
    require_once plugin_dir_path(__FILE__) . 'includes/class-db-manager.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-form-builder.php'; // New form builder class
    require_once plugin_dir_path(__FILE__) . 'includes/class-relationship-manager.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-data-manager.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-settings-manager.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-email-notifier.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-enhanced-email.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-approval-manager.php';
    require_once plugin_dir_path(__FILE__) . 'includes/class-ajax-handler.php';
    require_once plugin_dir_path(__FILE__) . 'includes/admin-post-handlers.php';
    require_once plugin_dir_path(__FILE__) . 'includes/ajax-table-handlers.php'; // AJAX handlers for tables
} catch (Error $e) {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log('DAB Plugin Error loading core files: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine());
    }
    return;
}
// Dashboard manager classes
require_once plugin_dir_path(__FILE__) . 'includes/class-admin-dashboard.php'; // Admin dashboard widgets
require_once plugin_dir_path(__FILE__) . 'includes/class-data-dashboard-manager.php'; // Enhanced data management dashboard
require_once plugin_dir_path(__FILE__) . 'includes/class-simple-dashboard-manager.php'; // Simple dashboard builder
require_once plugin_dir_path(__FILE__) . 'includes/class-forms-manager.php'; // Forms manager
require_once plugin_dir_path(__FILE__) . 'includes/class-inline-table-ajax.php'; // Inline table AJAX handler
require_once plugin_dir_path(__FILE__) . 'includes/class-wp-fields.php'; // WordPress role and user fields
require_once plugin_dir_path(__FILE__) . 'includes/class-role-permissions-manager.php'; // Role-based permissions
require_once plugin_dir_path(__FILE__) . 'includes/class-autoincrement-field.php'; // Autoincrement field handler
require_once plugin_dir_path(__FILE__) . 'includes/class-enhanced-currency-field.php'; // Enhanced currency field
require_once plugin_dir_path(__FILE__) . 'includes/class-multiselect-field.php'; // Multi-select dropdown field
require_once plugin_dir_path(__FILE__) . 'includes/class-social-media-field.php'; // Social media links field
require_once plugin_dir_path(__FILE__) . 'includes/class-signature-field.php'; // Signature capture field
require_once plugin_dir_path(__FILE__) . 'includes/class-media-field.php'; // Audio/Video media field
require_once plugin_dir_path(__FILE__) . 'includes/class-conditional-logic-field.php'; // Conditional logic field
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-form.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-view.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-approval-dashboard.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-dashboard.php';
require_once plugin_dir_path(__FILE__) . 'admin/direct-formula-builder.php'; // Visual formula builder
require_once plugin_dir_path(__FILE__) . 'admin/class-wizard-manager.php'; // Guided setup wizards
require_once plugin_dir_path(__FILE__) . 'includes/class-template-manager.php'; // Business application templates

// Load Payment Gateway Files
require_once plugin_dir_path(__FILE__) . 'includes/class-payment-gateway.php';
require_once plugin_dir_path(__FILE__) . 'includes/gateways/class-stripe-gateway.php';
require_once plugin_dir_path(__FILE__) . 'includes/gateways/class-paypal-gateway.php';
require_once plugin_dir_path(__FILE__) . 'includes/gateways/class-paystack-gateway.php';

// Load Integration Files
require_once plugin_dir_path(__FILE__) . 'includes/integrations/class-google-sheets-integration.php';
require_once plugin_dir_path(__FILE__) . 'includes/integrations/class-zapier-integration.php';

// Load License Files
require_once plugin_dir_path(__FILE__) . 'includes/class-license-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-license-validator.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-license-api.php';

// Load Debug Manager
require_once plugin_dir_path(__FILE__) . 'includes/class-debug-manager.php';

// Load License Tester
require_once plugin_dir_path(__FILE__) . 'includes/class-license-tester.php';

// Load Health Check (only in admin)
if (is_admin()) {
    require_once plugin_dir_path(__FILE__) . 'includes/class-health-check.php';
}

// Load Phase 3: Analytics & Intelligence Classes
require_once plugin_dir_path(__FILE__) . 'includes/analytics/class-advanced-report-builder.php';
require_once plugin_dir_path(__FILE__) . 'includes/analytics/class-ai-insights-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/analytics/class-realtime-dashboard-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/analytics/class-report-scheduler.php';

// Load Frontend User Management System
require_once plugin_dir_path(__FILE__) . 'includes/class-frontend-user-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-user-dashboard-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-frontend-auth.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-frontend-installer.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-user-login.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-user-register.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-user-dashboard.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-user-profile.php';

// Load Chat System
require_once plugin_dir_path(__FILE__) . 'includes/class-chat-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-chat-groups-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/shortcode-chat.php';
require_once plugin_dir_path(__FILE__) . 'includes/chat-debug.php';

// Load WooCommerce Integration
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-woocommerce-integration.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-product-fields-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-customer-data-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-order-fields-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-sales-dashboard-manager.php';

// Load WooCommerce Phase 2 Integration
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-inventory-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-advanced-checkout-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-marketing-automation-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/woocommerce/class-enhanced-reporting-manager.php';

// Load Workflow System (Phase 1 Enhancement)
require_once plugin_dir_path(__FILE__) . 'includes/class-workflow-builder.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-workflow-executor.php';

// Load Modern UI Components (Phase 2 Enhancement)
require_once plugin_dir_path(__FILE__) . 'includes/modern-ui/class-kanban-field.php';
require_once plugin_dir_path(__FILE__) . 'includes/modern-ui/class-calendar-field.php';
require_once plugin_dir_path(__FILE__) . 'includes/modern-ui/class-timeline-field.php';
require_once plugin_dir_path(__FILE__) . 'includes/modern-ui/class-progress-field.php';

// Load Multi-Step Forms System (Phase 2 Enhancement)
require_once plugin_dir_path(__FILE__) . 'includes/forms/class-multistep-form-builder.php';
require_once plugin_dir_path(__FILE__) . 'includes/forms/class-form-wizard-manager.php';
require_once plugin_dir_path(__FILE__) . 'includes/forms/class-conditional-logic-engine.php';

// Load Enhanced Formula Engine (Phase 1 Enhancement)
require_once plugin_dir_path(__FILE__) . 'includes/class-enhanced-formula-engine.php';

// Initialize Debug Manager
if (!function_exists('DAB_Debug')) {
    function DAB_Debug() {
        return DAB_Debug_Manager::instance();
    }
}

// Enqueue Admin Assets
add_action('admin_enqueue_scripts', function ($hook) {
    // Safely handle the hook parameter to prevent deprecated warnings
    $hook = dab_safe_string($hook);

    // Only load admin assets on plugin pages
    if ($hook === '' || dab_safe_strpos($hook, 'dab_') === false) {
        return;
    }

    // Enqueue modern design system and UI
    wp_enqueue_style('dab-design-system', plugin_dir_url(__FILE__) . 'assets/css/design-system.css', array(), DAB_VERSION);
    wp_enqueue_style('dab-modern-ui', plugin_dir_url(__FILE__) . 'assets/css/modern-ui.css', array('dab-design-system'), DAB_VERSION);
    wp_enqueue_style('dab-modern-admin', plugin_dir_url(__FILE__) . 'assets/css/modern-admin.css', array('dab-modern-ui'), DAB_VERSION);

    // Keep the original admin style for backward compatibility
    wp_enqueue_style('dab-admin-style', plugin_dir_url(__FILE__) . 'assets/css/admin-style.css', array('dab-modern-admin'), DAB_VERSION);

    // Enqueue error handler first
    wp_enqueue_script('dab-error-handler', plugin_dir_url(__FILE__) . 'assets/js/error-handler.js', array('jquery'), DAB_VERSION, true);

    // Enqueue modern animations
    wp_enqueue_script('dab-modern-animations', plugin_dir_url(__FILE__) . 'assets/js/modern-animations.js', array('jquery', 'dab-error-handler'), DAB_VERSION, true);
    wp_enqueue_script('dab-admin-scripts', plugin_dir_url(__FILE__) . 'assets/js/admin-scripts.js', array('jquery', 'dab-modern-animations'), DAB_VERSION, true);

    // Enqueue share modal for admin pages that might need it
    wp_enqueue_script('dab-share-modal', plugin_dir_url(__FILE__) . 'assets/js/share-modal.js', array('jquery', 'dab-error-handler'), DAB_VERSION, true);

    // Localize script with common variables
    wp_localize_script('dab-admin-scripts', 'dab_vars', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_admin_nonce'),
        'plugin_url' => plugin_dir_url(__FILE__),
        'is_admin' => true
    ));

    // Add jQuery UI for sortable functionality
    wp_enqueue_script('jquery-ui-sortable');

    // Enqueue dropdown builder script on the fields page
    if ($hook !== '' && dab_safe_strpos($hook, 'dab_fields') !== false) {
        wp_enqueue_script('dab-dropdown-builder', plugin_dir_url(__FILE__) . 'assets/js/dropdown-builder.js', array('jquery', 'jquery-ui-sortable'), DAB_VERSION, true);
    }

    // Enqueue field sortable script and styles on the forms page
    if ($hook !== '' && dab_safe_strpos($hook, 'dab_forms') !== false) {
        // Field sortable
        wp_enqueue_style('dab-field-sortable', plugin_dir_url(__FILE__) . 'assets/css/field-sortable.css', array(), DAB_VERSION);
        wp_enqueue_script('dab-field-sortable', plugin_dir_url(__FILE__) . 'assets/js/field-sortable.js', array('jquery', 'jquery-ui-sortable'), DAB_VERSION, true);

        // Add nonce for field order AJAX
        wp_localize_script('dab-field-sortable', 'dab_field_order', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dab_field_order_nonce')
        ));

        // Conditional logic builder
        wp_enqueue_style('dab-conditional-logic-builder', plugin_dir_url(__FILE__) . 'assets/css/conditional-logic-builder.css', array(), DAB_VERSION);
        wp_enqueue_script('dab-conditional-logic-builder', plugin_dir_url(__FILE__) . 'assets/js/conditional-logic-builder.js', array('jquery'), DAB_VERSION, true);

        // Dashicons for the builder
        wp_enqueue_style('dashicons');
    }
});

// Enqueue Frontend Assets
add_action('wp_enqueue_scripts', function () {
    // Only load frontend assets when needed (when shortcodes are present)
    global $post;
    $load_assets = false;

    if (is_a($post, 'WP_Post') && !empty($post->post_content)) {
        // Safely handle post_content to prevent deprecated warnings
        $post_content = dab_safe_string($post->post_content);

        // Check if any of our shortcodes are used in the content
        $shortcodes = array('dab_form', 'dab_view', 'dab_dashboard', 'dab_approval_dashboard', 'dab_user_login', 'dab_user_register', 'dab_user_dashboard', 'dab_user_profile', 'dab_chat');
        foreach ($shortcodes as $shortcode) {
            if (has_shortcode($post_content, $shortcode)) {
                $load_assets = true;
                break;
            }
        }
    }

    // Allow filtering of the load_assets flag
    $load_assets = apply_filters('dab_load_frontend_assets', $load_assets);

    if (!$load_assets) {
        return;
    }

    // Enqueue modern design system and UI
    wp_enqueue_style('dab-design-system', plugin_dir_url(__FILE__) . 'assets/css/design-system.css', array(), DAB_VERSION);
    wp_enqueue_style('dab-modern-ui', plugin_dir_url(__FILE__) . 'assets/css/modern-ui.css', array('dab-design-system'), DAB_VERSION);
    wp_enqueue_style('dab-modern-frontend', plugin_dir_url(__FILE__) . 'assets/css/modern-frontend.css', array('dab-modern-ui'), DAB_VERSION);
    wp_enqueue_style('dab-modern-dashboard', plugin_dir_url(__FILE__) . 'assets/css/modern-dashboard.css', array('dab-modern-ui'), DAB_VERSION);

    // Keep original styles for backward compatibility
    wp_enqueue_style('dab-frontend-style', plugin_dir_url(__FILE__) . 'assets/css/frontend-style.css', array('dab-modern-frontend'), DAB_VERSION);
    wp_enqueue_style('dab-enhanced-table-search', plugin_dir_url(__FILE__) . 'assets/css/enhanced-table-search.css', array(), DAB_VERSION);
    wp_enqueue_style('dab-inline-table', plugin_dir_url(__FILE__) . 'assets/css/inline-table.css', array(), DAB_VERSION);
    wp_enqueue_style('dab-enhanced-inline-table', plugin_dir_url(__FILE__) . 'assets/css/enhanced-inline-table.css', array(), DAB_VERSION);

    // Scripts
    wp_enqueue_script('jquery');

    // Enqueue error handler first
    wp_enqueue_script('dab-error-handler', plugin_dir_url(__FILE__) . 'assets/js/error-handler.js', array('jquery'), DAB_VERSION, true);

    // Enqueue modern animations
    wp_enqueue_script('dab-modern-animations', plugin_dir_url(__FILE__) . 'assets/js/modern-animations.js', array('jquery', 'dab-error-handler'), DAB_VERSION, true);

    // Enqueue share modal for frontend pages
    wp_enqueue_script('dab-share-modal', plugin_dir_url(__FILE__) . 'assets/js/share-modal.js', array('jquery', 'dab-error-handler'), DAB_VERSION, true);

    // Original scripts
    wp_enqueue_script('dab-frontend-view', plugin_dir_url(__FILE__) . 'assets/js/frontend-view.js', array('jquery', 'dab-modern-animations'), DAB_VERSION, true);
    wp_enqueue_script('dab-advanced-dropdown', plugin_dir_url(__FILE__) . 'assets/js/advanced-dropdown.js', array('jquery'), DAB_VERSION, true);
    wp_enqueue_script('dab-form-validation', plugin_dir_url(__FILE__) . 'assets/js/form-validation.js', array('jquery'), DAB_VERSION, true);
    wp_enqueue_script('dab-enhanced-table-search', plugin_dir_url(__FILE__) . 'assets/js/enhanced-table-search.js', array('jquery'), DAB_VERSION, true);
    wp_enqueue_script('dab-table-export', plugin_dir_url(__FILE__) . 'assets/js/table-export.js', array('jquery'), DAB_VERSION, true);
    wp_enqueue_script('dab-inline-table', plugin_dir_url(__FILE__) . 'assets/js/inline-table.js', array('jquery'), DAB_VERSION, true);
    wp_enqueue_script('dab-enhanced-inline-table', plugin_dir_url(__FILE__) . 'assets/js/enhanced-inline-table.js', array('jquery'), DAB_VERSION, true);

    // Localize scripts with all necessary nonces
    wp_localize_script('dab-frontend-view', 'dab_vars', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_dropdown_nonce'),
        'view_nonce' => wp_create_nonce('dab_view_nonce'),
        'form_nonce' => wp_create_nonce('dab_form_nonce'),
        'dashboard_nonce' => wp_create_nonce('dab_dashboard_nonce'),
        'approval_nonce' => wp_create_nonce('dab_approval_nonce'),
        'is_admin' => false
    ));

    // Localize inline table script
    wp_localize_script('dab-inline-table', 'dabVars', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('dab_inline_table_nonce')
    ));
});

// Activation Hook
register_activation_hook(__FILE__, function () {
    // Prevent any output during activation
    ob_start();

    try {
        if (class_exists('DAB_DB_Manager')) {
            DAB_DB_Manager::create_base_tables();
            DAB_DB_Manager::ensure_all_field_columns_exist(); // ✅ Auto-column creation for all existing fields
        }

        if (class_exists('DAB_Simple_Dashboard_Manager')) {
            DAB_Simple_Dashboard_Manager::create_dashboard_tables(); // Create dashboard tables
        }

        if (class_exists('DAB_Frontend_User_Manager')) {
            DAB_Frontend_User_Manager::create_tables(); // Create frontend user tables
        }

        if (class_exists('DAB_User_Dashboard_Manager')) {
            DAB_User_Dashboard_Manager::create_tables(); // Create user dashboard tables
        }

        if (class_exists('DAB_Chat_Manager')) {
            DAB_Chat_Manager::create_tables(); // Create chat tables
        }

        if (class_exists('DAB_Chat_Groups_Manager')) {
            DAB_Chat_Groups_Manager::create_tables(); // Create chat groups tables
        }

        // Create WooCommerce integration tables
        if (class_exists('DAB_Product_Fields_Manager')) {
            DAB_Product_Fields_Manager::create_tables();
        }
        if (class_exists('DAB_Customer_Data_Manager')) {
            DAB_Customer_Data_Manager::create_tables();
        }
        if (class_exists('DAB_Order_Fields_Manager')) {
            DAB_Order_Fields_Manager::create_tables();
        }
        if (class_exists('DAB_Sales_Dashboard_Manager')) {
            DAB_Sales_Dashboard_Manager::create_tables();
        }

        // Create WooCommerce Phase 2 integration tables
        if (class_exists('DAB_Inventory_Manager')) {
            DAB_Inventory_Manager::create_tables();
        }
        if (class_exists('DAB_Advanced_Checkout_Manager')) {
            DAB_Advanced_Checkout_Manager::create_tables();
        }
        if (class_exists('DAB_Marketing_Automation_Manager')) {
            DAB_Marketing_Automation_Manager::create_tables();
        }
        if (class_exists('DAB_Enhanced_Reporting_Manager')) {
            DAB_Enhanced_Reporting_Manager::create_tables();
        }

        // Create Workflow System tables (Phase 1 Enhancement)
        if (class_exists('DAB_Workflow_Builder')) {
            DAB_Workflow_Builder::create_tables();
        }

        // Create Modern UI Components tables (Phase 2 Enhancement)
        if (class_exists('DAB_Kanban_Field')) {
            DAB_Kanban_Field::create_tables();
        }
        if (class_exists('DAB_Calendar_Field')) {
            DAB_Calendar_Field::create_tables();
        }
        if (class_exists('DAB_Timeline_Field')) {
            DAB_Timeline_Field::create_tables();
        }
        if (class_exists('DAB_Progress_Field')) {
            DAB_Progress_Field::create_tables();
        }

        // Create Multi-Step Forms tables (Phase 2 Enhancement)
        if (class_exists('DAB_Multistep_Form_Builder')) {
            DAB_Multistep_Form_Builder::create_tables();
        }

        // Create Phase 3: Analytics & Intelligence tables
        if (class_exists('DAB_Advanced_Report_Builder')) {
            DAB_Advanced_Report_Builder::create_tables();
        }
        if (class_exists('DAB_AI_Insights_Manager')) {
            DAB_AI_Insights_Manager::create_tables();
        }
        if (class_exists('DAB_Realtime_Dashboard_Manager')) {
            DAB_Realtime_Dashboard_Manager::create_tables();
        }
        if (class_exists('DAB_Report_Scheduler')) {
            DAB_Report_Scheduler::create_tables();
        }

        // Create Template System tables
        if (class_exists('DAB_Template_Manager')) {
            DAB_Template_Manager::create_tables();
        }
    } catch (Exception $e) {
        // Log activation error
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Plugin Activation Error: ' . $e->getMessage());
        }
        // Don't prevent activation, just log the error
    } finally {
        // Clean up output buffer to prevent any unexpected output
        $output = ob_get_clean();
        if (!empty($output) && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Plugin Activation Output: ' . $output);
        }
    }
});

// Run database updates on plugin load to ensure all required columns exist
add_action('plugins_loaded', function() {
    // Add error handling for database operations
    try {
        // Check for advanced field columns
        if (class_exists('DAB_DB_Manager')) {
            DAB_DB_Manager::ensure_advanced_field_columns_exist();
        }

        // Only run this check occasionally to avoid performance impact
        $last_check = get_option('dab_db_structure_check', 0);
        $current_time = time();

        // Check once a day at most
        if ($current_time - $last_check > 86400) {
            // Check and fix database structure
            if (class_exists('DAB_DB_Manager')) {
                DAB_DB_Manager::create_base_tables();
                DAB_DB_Manager::ensure_all_field_columns_exist();
            }

            // Update the last check time
            update_option('dab_db_structure_check', $current_time);
        }
    } catch (Exception $e) {
        // Log error but don't break the site
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Plugin Database Error: ' . $e->getMessage());
        }
    }
});

// AJAX: Fetch Fields for Creating Views
add_action('wp_ajax_dab_get_fields', function () {
    // Verify nonce for security
    if (!isset($_REQUEST['nonce']) || !wp_verify_nonce($_REQUEST['nonce'], 'dab_admin_nonce')) {
        wp_send_json_error('Security check failed', 403);
        return;
    }

    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_send_json_error('Unauthorized', 403);
        return;
    }

    if (!isset($_GET['table_id'])) {
        wp_send_json_error('No Table Selected', 400);
        return;
    }

    global $wpdb;
    $table_id = intval($_GET['table_id']);
    $fields_table = $wpdb->prefix . 'dab_fields';

    $fields = $wpdb->get_results(
        $wpdb->prepare("SELECT field_label, field_slug FROM $fields_table WHERE table_id = %d", $table_id)
    );

    if (!empty($fields)) {
        wp_send_json($fields);
    } else {
        wp_send_json([]);
    }
});

// AJAX: Load View Table (Frontend AJAX Filter)
add_action('wp_ajax_dab_load_view', 'dab_load_view_ajax');
add_action('wp_ajax_nopriv_dab_load_view', 'dab_load_view_ajax');

// Frontend Users CSV Export
add_action('wp_ajax_dab_export_users_csv', array('DAB_Frontend_User_Manager', 'export_users_csv'));

function dab_load_view_ajax() {
    global $wpdb;

    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'dab_view_nonce')) {
        wp_send_json_error('Security check failed');
        return;
    }

    if (!isset($_POST['view_id'])) {
        wp_send_json_error('No View Selected');
        return;
    }

    $view_id = intval($_POST['view_id']);
    $filter_field = isset($_POST['filter_field']) ? sanitize_key($_POST['filter_field']) : '';
    $filter_value = isset($_POST['filter_value']) ? sanitize_text_field($_POST['filter_value']) : '';
    $sort_field = isset($_POST['sort_field']) ? sanitize_key($_POST['sort_field']) : '';
    $sort_order = isset($_POST['sort_order']) ? sanitize_text_field($_POST['sort_order']) : 'DESC';

    $views_table = $wpdb->prefix . 'dab_views';
    $tables_table = $wpdb->prefix . 'dab_tables';
    $fields_table = $wpdb->prefix . 'dab_fields';

    $view = $wpdb->get_row($wpdb->prepare("SELECT * FROM $views_table WHERE id = %d", $view_id));
    if (!$view) {
        wp_send_json_error('View not found');
        return;
    }

    // Check if view is public or user has permission
    if (empty($view->is_public) && !current_user_can('read')) {
        wp_send_json_error('You do not have permission to view this data');
        return;
    }

    $table = $wpdb->get_row($wpdb->prepare("SELECT * FROM $tables_table WHERE id = %d", $view->table_id));
    if (!$table) {
        wp_send_json_error('Table not found');
        return;
    }

    $data_table_name = $wpdb->prefix . 'dab_' . sanitize_title($table->table_slug);
    $selected_fields = maybe_unserialize($view->selected_fields);

    if (empty($selected_fields)) {
        $fields = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $fields_table WHERE table_id = %d ORDER BY field_order ASC, id ASC",
            $view->table_id
        ));
    } else {
        $fields = [];
        foreach ($selected_fields as $slug) {
            $field = $wpdb->get_row(
                $wpdb->prepare(
                    "SELECT * FROM $fields_table WHERE table_id = %d AND field_slug = %s",
                    $view->table_id,
                    $slug
                )
            );
            if ($field) {
                $fields[] = $field;
            }
        }
    }

    // Build WHERE clause
    $where = "WHERE 1=1";
    if (!empty($filter_field) && !empty($filter_value)) {
        // Validate filter_field exists in the table to prevent SQL injection
        $field_exists = false;
        foreach ($fields as $field) {
            if ($field->field_slug === $filter_field) {
                $field_exists = true;
                break;
            }
        }

        if ($field_exists) {
            $where .= $wpdb->prepare(" AND `$filter_field` = %s", $filter_value);
        }
    }

    $order_by = "ORDER BY id DESC";
    if (!empty($sort_field)) {
        // Validate sort_field exists in the table to prevent SQL injection
        $field_exists = false;
        foreach ($fields as $field) {
            if ($field->field_slug === $sort_field) {
                $field_exists = true;
                break;
            }
        }

        if ($field_exists) {
            $sort_order = in_array(strtoupper($sort_order), ['ASC', 'DESC']) ? strtoupper($sort_order) : 'DESC';
            $order_by = "ORDER BY `$sort_field` $sort_order";
        }
    }

    // Use column names instead of * for better performance
    $columns = array('id');
    foreach ($fields as $field) {
        $columns[] = "`" . esc_sql($field->field_slug) . "`";
    }
    $column_list = implode(', ', $columns);

    $records = $wpdb->get_results("SELECT $column_list FROM $data_table_name $where $order_by");

    ob_start();
    if (!empty($records)) {
        echo '<table class="dab-view-table" style="width:100%; border-collapse: collapse;">';
        echo '<thead><tr>';
        foreach ($fields as $field) {
            echo '<th style="border: 1px solid #ccc; padding: 8px;">' . esc_html($field->field_label) . '</th>';
        }
        echo '</tr></thead><tbody>';
        foreach ($records as $record) {
            echo '<tr>';
            foreach ($fields as $field) {
                $value = isset($record->{$field->field_slug}) ? esc_html($record->{$field->field_slug}) : '';
                echo '<td style="border: 1px solid #ccc; padding: 8px;">' . $value . '</td>';
            }
            echo '</tr>';
        }
        echo '</tbody></table>';
    } else {
        echo '<p>No records found.</p>';
    }
    $html = ob_get_clean();

    wp_send_json_success(['html' => $html]);
}

/**
 * Register AJAX handlers
 */
function dab_register_ajax_handlers() {
    // Register approval manager AJAX handlers
    if (class_exists('DAB_Approval_Manager')) {
        DAB_Approval_Manager::register_ajax_handlers();
    }

    // Other AJAX handlers are registered in their respective files
}
add_action('init', 'dab_register_ajax_handlers');

/**
 * Initialize Dashboard Managers
 */
add_action('init', array('DAB_Data_Dashboard_Manager', 'init'));
add_action('init', array('DAB_Simple_Dashboard_Manager', 'init'));

// Check and update dashboard tables
add_action('init', array('DAB_Simple_Dashboard_Manager', 'check_and_update_dashboard_tables'));

/**
 * Initialize Role Permissions Manager
 */
add_action('init', array('DAB_Role_Permissions_Manager', 'init'));

/**
 * Initialize Frontend User Management System
 */
add_action('init', array('DAB_Frontend_User_Manager', 'init'));
add_action('init', array('DAB_User_Dashboard_Manager', 'init'));
add_action('init', array('DAB_Frontend_Auth', 'init'));
add_action('init', array('DAB_Frontend_Installer', 'init'));

/**
 * Initialize Chat System
 */
add_action('init', array('DAB_Chat_Manager', 'init'));
add_action('init', array('DAB_Chat_Groups_Manager', 'init'));

/**
 * Initialize WooCommerce Integration
 */
add_action('plugins_loaded', array('DAB_WooCommerce_Integration', 'init'), 20); // Load after WooCommerce
add_action('init', array('DAB_Product_Fields_Manager', 'init'));
add_action('init', array('DAB_Customer_Data_Manager', 'init'));
add_action('init', array('DAB_Order_Fields_Manager', 'init'));

// Initialize Sales Dashboard Manager with safety check
add_action('init', function() {
    if (class_exists('DAB_Sales_Dashboard_Manager')) {
        DAB_Sales_Dashboard_Manager::init();
    }
});

/**
 * Initialize WooCommerce Phase 2 Integration
 */
add_action('init', array('DAB_Inventory_Manager', 'init'));
add_action('init', array('DAB_Advanced_Checkout_Manager', 'init'));
add_action('init', array('DAB_Marketing_Automation_Manager', 'init'));
add_action('init', array('DAB_Enhanced_Reporting_Manager', 'init'));

/**
 * Initialize Workflow System (Phase 1 Enhancement)
 */
add_action('init', array('DAB_Workflow_Builder', 'init'));
add_action('init', array('DAB_Workflow_Executor', 'init'));

/**
 * Initialize Enhanced Formula Engine (Phase 1 Enhancement)
 */
add_action('init', array('DAB_Enhanced_Formula_Engine', 'init'));

/**
 * Initialize Modern UI Components (Phase 2 Enhancement)
 */
add_action('init', array('DAB_Kanban_Field', 'init'));
add_action('init', array('DAB_Calendar_Field', 'init'));
add_action('init', array('DAB_Timeline_Field', 'init'));
add_action('init', array('DAB_Progress_Field', 'init'));

/**
 * Initialize Multi-Step Forms System (Phase 2 Enhancement)
 */
add_action('init', array('DAB_Multistep_Form_Builder', 'init'));
add_action('init', array('DAB_Form_Wizard_Manager', 'init'));
add_action('init', array('DAB_Conditional_Logic_Engine', 'init'));

/**
 * Initialize Analytics & Intelligence System (Phase 3 Enhancement)
 */
add_action('init', array('DAB_Advanced_Report_Builder', 'init'));
add_action('init', array('DAB_AI_Insights_Manager', 'init'));

// Initialize Realtime Dashboard Manager with safety check
add_action('init', function() {
    if (class_exists('DAB_Realtime_Dashboard_Manager')) {
        DAB_Realtime_Dashboard_Manager::init();
    }
});

add_action('init', array('DAB_Report_Scheduler', 'init'));

/**
 * Initialize Template System
 */
add_action('init', array('DAB_Template_Manager', 'init'));

// Ensure Template Manager is initialized with higher priority
add_action('admin_init', function() {
    if (class_exists('DAB_Template_Manager')) {
        // Re-initialize to ensure AJAX handlers are registered
        DAB_Template_Manager::init();
    }
}, 5);

// Clean up output buffer at the end of plugin loading
add_action('plugins_loaded', function() {
    global $original_error_reporting;

    // Restore original error reporting
    if (isset($original_error_reporting)) {
        error_reporting($original_error_reporting);
    }

    // Restore default error handler
    restore_error_handler();

    if (!defined('DOING_AJAX') && !defined('WP_CLI') && ob_get_level() > 0) {
        $output = ob_get_clean();
        if (!empty($output) && defined('WP_DEBUG') && WP_DEBUG) {
            error_log('DAB Plugin Loading Output: ' . $output);
        }
    }
}, 999);
