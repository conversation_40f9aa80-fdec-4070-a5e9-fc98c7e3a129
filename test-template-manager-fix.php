<?php
/**
 * Test Template Manager Deprecation Fixes
 * 
 * This file tests the fixes for PHP 8.1+ deprecation warnings
 * in the Template Manager and related components.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

echo "<h2>Testing Template Manager Deprecation Fixes</h2>\n";

// Test 1: Safe string function
echo "<h3>Test 1: Safe String Function</h3>\n";
if (function_exists('dab_safe_string')) {
    echo "✅ dab_safe_string function exists\n";
    
    // Test with null
    $result = dab_safe_string(null);
    echo "- dab_safe_string(null) = '" . $result . "' (should be empty string)\n";
    
    // Test with string
    $result = dab_safe_string('test');
    echo "- dab_safe_string('test') = '" . $result . "' (should be 'test')\n";
    
    // Test with number
    $result = dab_safe_string(123);
    echo "- dab_safe_string(123) = '" . $result . "' (should be '123')\n";
} else {
    echo "❌ dab_safe_string function not found\n";
}

// Test 2: Safe strpos function
echo "<h3>Test 2: Safe Strpos Function</h3>\n";
if (function_exists('dab_safe_strpos')) {
    echo "✅ dab_safe_strpos function exists\n";
    
    // Test with null haystack
    $result = dab_safe_strpos(null, 'test');
    echo "- dab_safe_strpos(null, 'test') = " . ($result === false ? 'false' : $result) . " (should be false)\n";
    
    // Test with valid strings
    $result = dab_safe_strpos('hello world', 'world');
    echo "- dab_safe_strpos('hello world', 'world') = " . $result . " (should be 6)\n";
} else {
    echo "❌ dab_safe_strpos function not found\n";
}

// Test 3: Safe str_replace function
echo "<h3>Test 3: Safe Str_replace Function</h3>\n";
if (function_exists('dab_safe_str_replace')) {
    echo "✅ dab_safe_str_replace function exists\n";
    
    // Test with null subject
    $result = dab_safe_str_replace('test', 'replacement', null);
    echo "- dab_safe_str_replace('test', 'replacement', null) = '" . $result . "' (should be empty string)\n";
    
    // Test with valid strings
    $result = dab_safe_str_replace('world', 'PHP', 'hello world');
    echo "- dab_safe_str_replace('world', 'PHP', 'hello world') = '" . $result . "' (should be 'hello PHP')\n";
} else {
    echo "❌ dab_safe_str_replace function not found\n";
}

// Test 4: Template Manager class
echo "<h3>Test 4: Template Manager Class</h3>\n";
if (class_exists('DAB_Template_Manager')) {
    echo "✅ DAB_Template_Manager class exists\n";
    
    // Test enqueue_scripts method with null parameter
    echo "- Testing enqueue_scripts with null parameter...\n";
    try {
        // Capture any output or errors
        ob_start();
        DAB_Template_Manager::enqueue_scripts(null);
        $output = ob_get_clean();
        echo "✅ enqueue_scripts(null) executed without errors\n";
        if (!empty($output)) {
            echo "- Output: " . $output . "\n";
        }
    } catch (Exception $e) {
        echo "❌ enqueue_scripts(null) threw exception: " . $e->getMessage() . "\n";
    }
    
    // Test enqueue_scripts method with empty string
    echo "- Testing enqueue_scripts with empty string...\n";
    try {
        ob_start();
        DAB_Template_Manager::enqueue_scripts('');
        $output = ob_get_clean();
        echo "✅ enqueue_scripts('') executed without errors\n";
        if (!empty($output)) {
            echo "- Output: " . $output . "\n";
        }
    } catch (Exception $e) {
        echo "❌ enqueue_scripts('') threw exception: " . $e->getMessage() . "\n";
    }
    
    // Test enqueue_scripts method with valid hook
    echo "- Testing enqueue_scripts with valid hook...\n";
    try {
        ob_start();
        DAB_Template_Manager::enqueue_scripts('dab_app_templates');
        $output = ob_get_clean();
        echo "✅ enqueue_scripts('dab_app_templates') executed without errors\n";
        if (!empty($output)) {
            echo "- Output: " . $output . "\n";
        }
    } catch (Exception $e) {
        echo "❌ enqueue_scripts('dab_app_templates') threw exception: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ DAB_Template_Manager class not found\n";
}

// Test 5: Error handler
echo "<h3>Test 5: Error Handler</h3>\n";
$old_error_reporting = error_reporting();
error_reporting(E_ALL);

echo "- Testing error handler with deprecated function calls...\n";

// These would normally trigger deprecation warnings in PHP 8.1+
if (version_compare(PHP_VERSION, '8.1', '>=')) {
    echo "- Testing strpos with null (should be handled gracefully)\n";
    // This should not produce a warning due to our error handler
    @strpos(null, 'test');
    
    echo "- Testing str_replace with null (should be handled gracefully)\n";
    // This should not produce a warning due to our error handler
    @str_replace('test', 'replacement', null);
    
    echo "✅ Deprecated function calls handled without visible warnings\n";
} else {
    echo "- PHP version is " . PHP_VERSION . " (< 8.1), deprecation warnings not applicable\n";
}

// Restore error reporting
error_reporting($old_error_reporting);

echo "<h3>Test Summary</h3>\n";
echo "All tests completed. Check the output above for any issues.\n";
echo "If you see ✅ for all tests, the deprecation fixes are working correctly.\n";
echo "If you see ❌ for any tests, there may be issues that need to be addressed.\n";

// Add some debugging information
echo "<h3>Debug Information</h3>\n";
echo "- PHP Version: " . PHP_VERSION . "\n";
echo "- WordPress Version: " . (defined('WP_VERSION') ? WP_VERSION : 'Unknown') . "\n";
echo "- DAB Plugin Version: " . (defined('DAB_VERSION') ? DAB_VERSION : 'Unknown') . "\n";
echo "- Error Reporting Level: " . error_reporting() . "\n";
echo "- WP_DEBUG: " . (defined('WP_DEBUG') && WP_DEBUG ? 'Enabled' : 'Disabled') . "\n";
echo "- WP_DEBUG_LOG: " . (defined('WP_DEBUG_LOG') && WP_DEBUG_LOG ? 'Enabled' : 'Disabled') . "\n";
