/**
 * Database App Builder - JavaScript Error Handler
 * 
 * Provides comprehensive error handling and defensive programming
 * for all JavaScript functionality in the plugin
 */

(function($) {
    'use strict';

    // Global error handler
    window.DAB_ErrorHandler = {
        
        /**
         * Initialize error handling
         */
        init: function() {
            this.setupGlobalErrorHandlers();
            this.setupDOMHelpers();
            this.setupEventHelpers();
            this.preventCommonErrors();
        },

        /**
         * Setup global error handlers
         */
        setupGlobalErrorHandlers: function() {
            // Handle uncaught JavaScript errors
            window.addEventListener('error', function(e) {
                console.warn('DAB: JavaScript Error:', {
                    message: e.message,
                    filename: e.filename,
                    lineno: e.lineno,
                    colno: e.colno,
                    error: e.error
                });
                
                // Don't let errors break the page
                return true;
            }, { passive: true });

            // Handle unhandled promise rejections
            window.addEventListener('unhandledrejection', function(e) {
                console.warn('DAB: Unhandled Promise Rejection:', e.reason);
                
                // Prevent the default browser behavior
                e.preventDefault();
            }, { passive: true });

            // jQuery error handler
            if (typeof $ !== 'undefined') {
                $(document).ajaxError(function(event, jqXHR, ajaxSettings, thrownError) {
                    console.warn('DAB: AJAX Error:', {
                        url: ajaxSettings.url,
                        status: jqXHR.status,
                        error: thrownError,
                        response: jqXHR.responseText
                    });
                });
            }
        },

        /**
         * Setup DOM helpers
         */
        setupDOMHelpers: function() {
            // Safe element selector
            window.DAB_safeSelect = function(selector, context) {
                try {
                    const ctx = context || document;
                    if (!ctx || typeof ctx.querySelector !== 'function') {
                        return null;
                    }
                    return ctx.querySelector(selector);
                } catch (e) {
                    console.warn('DAB: Invalid selector:', selector, e);
                    return null;
                }
            };

            // Safe element selector (all)
            window.DAB_safeSelectAll = function(selector, context) {
                try {
                    const ctx = context || document;
                    if (!ctx || typeof ctx.querySelectorAll !== 'function') {
                        return [];
                    }
                    return Array.from(ctx.querySelectorAll(selector));
                } catch (e) {
                    console.warn('DAB: Invalid selector:', selector, e);
                    return [];
                }
            };

            // Safe element by ID
            window.DAB_safeGetById = function(id) {
                try {
                    if (!id || typeof id !== 'string') {
                        return null;
                    }
                    return document.getElementById(id);
                } catch (e) {
                    console.warn('DAB: Error getting element by ID:', id, e);
                    return null;
                }
            };
        },

        /**
         * Setup event helpers
         */
        setupEventHelpers: function() {
            // Safe event listener
            window.DAB_safeAddEventListener = function(element, event, handler, options) {
                try {
                    if (!element || typeof element.addEventListener !== 'function') {
                        return false;
                    }
                    
                    const safeOptions = options || { passive: true };
                    element.addEventListener(event, function(e) {
                        try {
                            handler.call(this, e);
                        } catch (error) {
                            console.warn('DAB: Event handler error:', error);
                        }
                    }, safeOptions);
                    
                    return true;
                } catch (e) {
                    console.warn('DAB: Error adding event listener:', e);
                    return false;
                }
            };

            // Safe jQuery event binding
            if (typeof $ !== 'undefined') {
                window.DAB_safeOn = function(selector, event, handler) {
                    try {
                        $(document).on(event, selector, function(e) {
                            try {
                                handler.call(this, e);
                            } catch (error) {
                                console.warn('DAB: jQuery event handler error:', error);
                            }
                        });
                        return true;
                    } catch (e) {
                        console.warn('DAB: Error binding jQuery event:', e);
                        return false;
                    }
                };
            }
        },

        /**
         * Prevent common errors
         */
        preventCommonErrors: function() {
            // Prevent null reference errors on common elements
            this.createSafeElementChecks();
            
            // Add passive event listeners to prevent scroll blocking warnings
            this.fixScrollBlockingEvents();
            
            // Handle missing dependencies gracefully
            this.handleMissingDependencies();
        },

        /**
         * Create safe element checks
         */
        createSafeElementChecks: function() {
            // Common elements that might not exist
            const commonSelectors = [
                '#share-modal',
                '.dab-modal',
                '.dab-share-btn',
                '.share-btn',
                '[data-action="share"]'
            ];

            commonSelectors.forEach(function(selector) {
                const elements = DAB_safeSelectAll(selector);
                if (elements.length === 0) {
                    // Create placeholder to prevent null reference errors
                    const placeholder = document.createElement('div');
                    placeholder.style.display = 'none';
                    placeholder.className = 'dab-placeholder';
                    placeholder.setAttribute('data-selector', selector);
                    document.body.appendChild(placeholder);
                }
            });
        },

        /**
         * Fix scroll blocking events
         */
        fixScrollBlockingEvents: function() {
            // Override common event listeners to use passive options
            const originalAddEventListener = EventTarget.prototype.addEventListener;
            
            EventTarget.prototype.addEventListener = function(type, listener, options) {
                // Events that should be passive by default
                const passiveEvents = ['scroll', 'wheel', 'touchstart', 'touchmove', 'touchend'];
                
                if (passiveEvents.includes(type) && typeof options !== 'object') {
                    options = { passive: true };
                } else if (passiveEvents.includes(type) && typeof options === 'object' && options.passive === undefined) {
                    options.passive = true;
                }
                
                return originalAddEventListener.call(this, type, listener, options);
            };
        },

        /**
         * Handle missing dependencies
         */
        handleMissingDependencies: function() {
            // Check for jQuery
            if (typeof $ === 'undefined' && typeof jQuery !== 'undefined') {
                window.$ = jQuery;
            }

            // Provide fallback for missing jQuery
            if (typeof $ === 'undefined') {
                window.$ = function(selector) {
                    return {
                        ready: function(callback) {
                            if (document.readyState === 'loading') {
                                document.addEventListener('DOMContentLoaded', callback);
                            } else {
                                callback();
                            }
                        },
                        on: function() { return this; },
                        off: function() { return this; },
                        find: function() { return this; },
                        hide: function() { return this; },
                        show: function() { return this; },
                        addClass: function() { return this; },
                        removeClass: function() { return this; },
                        length: 0
                    };
                };
            }

            // Check for common DAB variables
            if (typeof dab_vars === 'undefined') {
                window.dab_vars = {
                    ajax_url: '/wp-admin/admin-ajax.php',
                    nonce: '',
                    is_admin: false
                };
            }
        },

        /**
         * Safe AJAX wrapper
         */
        safeAjax: function(options) {
            if (typeof $ === 'undefined') {
                console.warn('DAB: jQuery not available for AJAX request');
                return;
            }

            const defaults = {
                type: 'POST',
                dataType: 'json',
                timeout: 30000,
                error: function(xhr, status, error) {
                    console.warn('DAB: AJAX Error:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                }
            };

            const settings = $.extend({}, defaults, options);
            
            try {
                return $.ajax(settings);
            } catch (e) {
                console.warn('DAB: AJAX Exception:', e);
                return null;
            }
        },

        /**
         * Safe modal operations
         */
        safeModal: {
            show: function(modalId) {
                const modal = DAB_safeGetById(modalId);
                if (modal && modal.style) {
                    modal.style.display = 'flex';
                    modal.setAttribute('aria-hidden', 'false');
                }
            },
            
            hide: function(modalId) {
                const modal = DAB_safeGetById(modalId);
                if (modal && modal.style) {
                    modal.style.display = 'none';
                    modal.setAttribute('aria-hidden', 'true');
                }
            },
            
            toggle: function(modalId) {
                const modal = DAB_safeGetById(modalId);
                if (modal && modal.style) {
                    const isVisible = modal.style.display === 'flex';
                    if (isVisible) {
                        this.hide(modalId);
                    } else {
                        this.show(modalId);
                    }
                }
            }
        }
    };

    // Initialize error handler when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            DAB_ErrorHandler.init();
        });
    } else {
        DAB_ErrorHandler.init();
    }

    // Also initialize with jQuery if available
    if (typeof $ !== 'undefined') {
        $(document).ready(function() {
            DAB_ErrorHandler.init();
        });
    }

})(typeof jQuery !== 'undefined' ? jQuery : undefined);
